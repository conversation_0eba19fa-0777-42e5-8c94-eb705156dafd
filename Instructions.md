# تعليمات تشغيل برنامج CleverMC

## الخطوات المطلوبة لتشغيل البرنامج:

### 1. إعداد قاعدة البيانات
قبل تشغيل البرنامج، يجب إنشاء قاعدة البيانات:

1. افتح SQL Server Management Studio أو أي أداة إدارة SQL Server
2. قم بتشغيل الملف `Database/CreateDatabase.sql` لإنشاء قاعدة البيانات والجداول
3. تأكد من أن SQL Server يعمل على المنفذ الافتراضي

### 2. تشغيل البرنامج

#### الطريقة الأولى - استخدام ملف التشغيل:
```bash
run.bat
```

#### الطريقة الثانية - استخدام سطر الأوامر:
```bash
dotnet restore
dotnet build
dotnet run
```

#### الطريقة الثالثة - من Visual Studio:
1. افتح الملف `CleverMC.sln` في Visual Studio
2. اضغط F5 أو اختر Debug > Start Debugging

### 3. استخدام البرنامج

عند تشغيل البرنامج ستظهر الواجهة الرئيسية مع:

- **الشريط الجانبي**: يحتوي على قائمة التنقل
- **لوحة المعلومات**: تعرض إحصائيات سريعة
- **المحتوى الرئيسي**: يتغير حسب القسم المختار

#### الأقسام المتاحة:
- ✅ **Tableau de Bord**: لوحة المعلومات الرئيسية
- ✅ **Patients**: إدارة المرضى (إضافة، تعديل، حذف، بحث)
- 🚧 **Médecins**: إدارة الأطباء (قيد التطوير)
- 🚧 **Rendez-vous**: إدارة المواعيد (قيد التطوير)
- 🚧 **Facturation**: إدارة الفواتير (قيد التطوير)
- 🚧 **Médicaments**: إدارة الأدوية (قيد التطوير)
- 🚧 **Rapports**: التقارير (قيد التطوير)

### 4. إدارة المرضى

في قسم المرضى يمكنك:

1. **عرض قائمة المرضى**: جميع المرضى المسجلين
2. **البحث**: استخدم مربع البحث للعثور على مريض معين
3. **إضافة مريض جديد**: اضغط على "Ajouter Patient"
4. **تعديل مريض**: اختر مريض واضغط "Modifier"
5. **حذف مريض**: اختر مريض واضغط "Supprimer" (حذف منطقي)

### 5. إضافة مريض جديد

عند إضافة مريض جديد، املأ المعلومات التالية:

#### معلومات شخصية (مطلوبة):
- الاسم الأول
- اسم العائلة
- تاريخ الميلاد

#### معلومات اختيارية:
- الجنس
- رقم الهاتف
- البريد الإلكتروني
- العنوان

#### معلومات الطوارئ:
- اسم جهة الاتصال
- رقم هاتف الطوارئ

#### معلومات طبية:
- فصيلة الدم
- الحساسيات
- التاريخ المرضي

### 6. استكشاف الأخطاء

#### مشكلة الاتصال بقاعدة البيانات:
- تأكد من تشغيل SQL Server
- تحقق من صحة سلسلة الاتصال في `appsettings.json`
- تأكد من إنشاء قاعدة البيانات `MedicalClinicDB`

#### مشكلة في البناء:
- تأكد من تثبيت .NET 8.0
- قم بتشغيل `dotnet restore` لاستعادة الحزم

### 7. الميزات المستقبلية

سيتم إضافة الميزات التالية في الإصدارات القادمة:
- إدارة كاملة للأطباء
- نظام حجز المواعيد
- إدارة الوصفات الطبية
- نظام الفواتير
- التقارير والإحصائيات
- نظام النسخ الاحتياطي

### 8. الدعم الفني

في حالة مواجهة أي مشاكل:
1. تحقق من ملف `README.md` للمزيد من التفاصيل
2. تأكد من تشغيل جميع الخدمات المطلوبة
3. راجع رسائل الخطأ في وحدة التحكم
