using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace CleverMC.Models
{
    public class Inventory
    {
        [Key]
        public int InventoryId { get; set; }

        [Required]
        public int MedicationId { get; set; }

        public int? QuantityReceived { get; set; }

        public int QuantityUsed { get; set; } = 0;

        public int? CurrentStock { get; set; }

        public DateTime LastUpdated { get; set; } = DateTime.Now;

        [StringLength(100)]
        public string? SupplierName { get; set; }

        [Column(TypeName = "decimal(10,2)")]
        public decimal? PurchasePrice { get; set; }

        // Navigation properties
        [ForeignKey("MedicationId")]
        public virtual Medication Medication { get; set; } = null!;
    }
}
