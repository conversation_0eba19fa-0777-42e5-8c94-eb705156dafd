using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace CleverMC.Models
{
    public class LabTest
    {
        [Key]
        public int TestId { get; set; }

        [Required]
        [StringLength(200)]
        public string TestName { get; set; } = string.Empty;

        [StringLength(100)]
        public string? TestCategory { get; set; }

        [Column(TypeName = "decimal(10,2)")]
        public decimal? TestCost { get; set; }

        [StringLength(200)]
        public string? NormalRange { get; set; }

        public string? Description { get; set; }

        public bool IsActive { get; set; } = true;

        // Navigation properties
        public virtual ICollection<PatientTest> PatientTests { get; set; } = new List<PatientTest>();
    }
}
