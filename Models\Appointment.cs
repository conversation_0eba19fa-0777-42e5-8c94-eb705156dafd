using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace CleverMC.Models
{
    public class Appointment
    {
        [Key]
        public int AppointmentId { get; set; }

        [Required]
        public int PatientId { get; set; }

        [Required]
        public int DoctorId { get; set; }

        [Required]
        public DateTime AppointmentDateTime { get; set; }

        [StringLength(20)]
        public string Status { get; set; } = "Réservé";

        [StringLength(50)]
        public string AppointmentType { get; set; } = "Consultation";

        public string? Notes { get; set; }

        [Column(TypeName = "decimal(10,2)")]
        public decimal? Fee { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.Now;

        public DateTime UpdatedAt { get; set; } = DateTime.Now;

        // Navigation properties
        [ForeignKey("PatientId")]
        public virtual Patient Patient { get; set; } = null!;

        [ForeignKey("DoctorId")]
        public virtual Doctor Doctor { get; set; } = null!;

        public virtual ICollection<MedicalRecord> MedicalRecords { get; set; } = new List<MedicalRecord>();
        public virtual ICollection<AppointmentRoom> AppointmentRooms { get; set; } = new List<AppointmentRoom>();
        public virtual ICollection<Billing> Bills { get; set; } = new List<Billing>();
    }
}
