-- إنشاء قاعدة البيانات
CREATE DATABASE MedicalClinicDB;
GO

USE MedicalClinicDB;
GO

-- جدول المرضى
CREATE TABLE PATIENTS (
    patient_id INT IDENTITY(1,1) PRIMARY KEY,
    first_name <PERSON>VA<PERSON><PERSON><PERSON>(50) NOT NULL,
    last_name <PERSON>VA<PERSON><PERSON><PERSON>(50) NOT NULL,
    birth_date DATE NOT NULL,
    gender NVARCHAR(10) CHECK (gender IN ('Homme', 'Femme')),
    phone NVARCHAR(20),
    email NVARCHAR(100),
    address NVARCHAR(500),
    emergency_contact_name NVARCHAR(100),
    emergency_contact_phone NVARCHAR(20),
    blood_type NVARCHAR(5) CHECK (blood_type IN ('A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-')),
    allergies NTEXT,
    medical_history NTEXT,
    registration_date DATE DEFAULT GETDATE(),
    is_active BIT DEFAULT 1
);

-- جدول الأطباء
CREATE TABLE DOCTORS (
    doctor_id INT IDENTITY(1,1) PRIMARY KEY,
    first_name NVA<PERSON>HAR(50) NOT NULL,
    last_name NVARCHAR(50) NOT NULL,
    specialty NVARCHAR(100) NOT NULL,
    license_number NVARCHAR(50) UNIQUE NOT NULL,
    phone NVARCHAR(20),
    email NVARCHAR(100),
    work_start_time TIME,
    work_end_time TIME,
    work_days NVARCHAR(100), -- الأحد,الاثنين,الثلاثاء... إلخ
    consultation_fee DECIMAL(10,2),
    hire_date DATE DEFAULT GETDATE(),
    is_active BIT DEFAULT 1
);

-- جدول الموظفين
CREATE TABLE STAFF (
    staff_id INT IDENTITY(1,1) PRIMARY KEY,
    first_name NVARCHAR(50) NOT NULL,
    last_name NVARCHAR(50) NOT NULL,
    role NVARCHAR(50) NOT NULL, -- ممرض، استقبال، مساعد طبي... إلخ
    phone NVARCHAR(20),
    email NVARCHAR(100),
    salary DECIMAL(10,2),
    hire_date DATE DEFAULT GETDATE(),
    is_active BIT DEFAULT 1
);

-- جدول الغرف
CREATE TABLE ROOMS (
    room_id INT IDENTITY(1,1) PRIMARY KEY,
    room_number NVARCHAR(10) UNIQUE NOT NULL,
    room_type NVARCHAR(50) NOT NULL, -- فحص، عمليات، انتظار... إلخ
    status NVARCHAR(20) DEFAULT 'Disponible' CHECK (status IN ('Disponible', 'Occupé', 'Maintenance')),
    equipment NTEXT,
    is_available BIT DEFAULT 1
);

-- جدول المواعيد
CREATE TABLE APPOINTMENTS (
    appointment_id INT IDENTITY(1,1) PRIMARY KEY,
    patient_id INT NOT NULL,
    doctor_id INT NOT NULL,
    appointment_datetime DATETIME NOT NULL,
    status NVARCHAR(20) DEFAULT 'Réservé' CHECK (status IN ('Réservé', 'Terminé', 'Annulé', 'Absent')),
    appointment_type NVARCHAR(50) DEFAULT 'Consultation' CHECK (appointment_type IN ('Consultation', 'Suivi', 'Urgence', 'Examen périodique')),
    notes NTEXT,
    fee DECIMAL(10,2),
    created_at DATETIME DEFAULT GETDATE(),
    updated_at DATETIME DEFAULT GETDATE(),
    FOREIGN KEY (patient_id) REFERENCES PATIENTS(patient_id),
    FOREIGN KEY (doctor_id) REFERENCES DOCTORS(doctor_id)
);

-- جدول السجلات الطبية
CREATE TABLE MEDICAL_RECORDS (
    record_id INT IDENTITY(1,1) PRIMARY KEY,
    patient_id INT NOT NULL,
    doctor_id INT NOT NULL,
    appointment_id INT,
    visit_date DATE DEFAULT GETDATE(),
    chief_complaint NTEXT, -- الشكوى الرئيسية
    symptoms NTEXT, -- الأعراض
    diagnosis NTEXT, -- التشخيص
    treatment_plan NTEXT, -- خطة العلاج
    prescriptions NTEXT, -- الوصفات
    doctor_notes NTEXT, -- ملاحظات الطبيب
    weight DECIMAL(5,2), -- الوزن بالكيلوجرام
    height DECIMAL(5,2), -- الطول بالسنتيمتر
    blood_pressure_systolic INT, -- ضغط الدم الانقباضي
    blood_pressure_diastolic INT, -- ضغط الدم الانبساطي
    temperature DECIMAL(4,1), -- درجة الحرارة
    heart_rate INT, -- معدل ضربات القلب
    FOREIGN KEY (patient_id) REFERENCES PATIENTS(patient_id),
    FOREIGN KEY (doctor_id) REFERENCES DOCTORS(doctor_id),
    FOREIGN KEY (appointment_id) REFERENCES APPOINTMENTS(appointment_id)
);

-- جدول الأدوية
CREATE TABLE MEDICATIONS (
    medication_id INT IDENTITY(1,1) PRIMARY KEY,
    medication_name NVARCHAR(200) NOT NULL,
    dosage_form NVARCHAR(50), -- أقراص، شراب، حقن... إلخ
    strength NVARCHAR(50), -- قوة الدواء (مثل 500mg)
    manufacturer NVARCHAR(100),
    description NTEXT,
    unit_price DECIMAL(10,2),
    stock_quantity INT DEFAULT 0,
    expiry_date DATE,
    is_active BIT DEFAULT 1
);

-- جدول الوصفات الطبية
CREATE TABLE PRESCRIPTIONS (
    prescription_id INT IDENTITY(1,1) PRIMARY KEY,
    record_id INT NOT NULL,
    medication_id INT NOT NULL,
    dosage NVARCHAR(100), -- الجرعة (مثل قرص واحد)
    frequency NVARCHAR(100), -- عدد المرات (مثل 3 مرات يومياً)
    duration_days INT, -- مدة العلاج بالأيام
    instructions NTEXT, -- تعليمات الاستخدام
    quantity INT, -- الكمية المطلوبة
    is_dispensed BIT DEFAULT 0, -- هل تم صرف الدواء
    prescribed_date DATE DEFAULT GETDATE(),
    FOREIGN KEY (record_id) REFERENCES MEDICAL_RECORDS(record_id),
    FOREIGN KEY (medication_id) REFERENCES MEDICATIONS(medication_id)
);

-- جدول المخزون
CREATE TABLE INVENTORY (
    inventory_id INT IDENTITY(1,1) PRIMARY KEY,
    medication_id INT NOT NULL,
    quantity_received INT, -- الكمية المستلمة
    quantity_used INT DEFAULT 0, -- الكمية المستخدمة
    current_stock INT, -- المخزون الحالي
    last_updated DATETIME DEFAULT GETDATE(),
    supplier_name NVARCHAR(100), -- اسم المورد
    purchase_price DECIMAL(10,2), -- سعر الشراء
    FOREIGN KEY (medication_id) REFERENCES MEDICATIONS(medication_id)
);

-- جدول التأمين
CREATE TABLE INSURANCE (
    insurance_id INT IDENTITY(1,1) PRIMARY KEY,
    patient_id INT NOT NULL,
    insurance_company NVARCHAR(100) NOT NULL,
    policy_number NVARCHAR(50) NOT NULL,
    coverage_percentage DECIMAL(5,2), -- نسبة التغطية
    policy_start_date DATE,
    policy_end_date DATE,
    is_active BIT DEFAULT 1,
    FOREIGN KEY (patient_id) REFERENCES PATIENTS(patient_id)
);

-- جدول الفحوصات المخبرية
CREATE TABLE LAB_TESTS (
    test_id INT IDENTITY(1,1) PRIMARY KEY,
    test_name NVARCHAR(200) NOT NULL,
    test_category NVARCHAR(100), -- فئة الفحص (دم، بول، أشعة... إلخ)
    test_cost DECIMAL(10,2),
    normal_range NVARCHAR(200), -- المعدل الطبيعي
    description NTEXT,
    is_active BIT DEFAULT 1
);

-- جدول فحوصات المرضى
CREATE TABLE PATIENT_TESTS (
    patient_test_id INT IDENTITY(1,1) PRIMARY KEY,
    patient_id INT NOT NULL,
    record_id INT,
    test_id INT NOT NULL,
    test_result NVARCHAR(500), -- نتيجة الفحص
    test_date DATE DEFAULT GETDATE(),
    status NVARCHAR(20) DEFAULT 'Demandé' CHECK (status IN ('Demandé', 'En cours', 'Terminé', 'Annulé')),
    technician_notes NTEXT, -- ملاحظات الفني
    cost DECIMAL(10,2),
    FOREIGN KEY (patient_id) REFERENCES PATIENTS(patient_id),
    FOREIGN KEY (record_id) REFERENCES MEDICAL_RECORDS(record_id),
    FOREIGN KEY (test_id) REFERENCES LAB_TESTS(test_id)
);

-- جدول الفواتير
CREATE TABLE BILLING (
    bill_id INT IDENTITY(1,1) PRIMARY KEY,
    patient_id INT NOT NULL,
    appointment_id INT,
    consultation_fee DECIMAL(10,2) DEFAULT 0,
    medication_cost DECIMAL(10,2) DEFAULT 0,
    test_cost DECIMAL(10,2) DEFAULT 0,
    other_charges DECIMAL(10,2) DEFAULT 0,
    total_amount AS (consultation_fee + medication_cost + test_cost + other_charges), -- حقل محسوب تلقائياً
    paid_amount DECIMAL(10,2) DEFAULT 0,
    balance AS (consultation_fee + medication_cost + test_cost + other_charges - paid_amount), -- حقل محسوب تلقائياً
    payment_method NVARCHAR(50) CHECK (payment_method IN ('Espèces', 'Carte de crédit', 'Virement bancaire', 'Assurance')),
    payment_status NVARCHAR(20) DEFAULT 'Non payé' CHECK (payment_status IN ('Non payé', 'Payé partiellement', 'Payé intégralement')),
    bill_date DATE DEFAULT GETDATE(),
    payment_date DATE,
    FOREIGN KEY (patient_id) REFERENCES PATIENTS(patient_id),
    FOREIGN KEY (appointment_id) REFERENCES APPOINTMENTS(appointment_id)
);

-- جدول حجز الغرف للمواعيد
CREATE TABLE APPOINTMENT_ROOMS (
    appointment_room_id INT IDENTITY(1,1) PRIMARY KEY,
    appointment_id INT NOT NULL,
    room_id INT NOT NULL,
    start_time DATETIME,
    end_time DATETIME,
    FOREIGN KEY (appointment_id) REFERENCES APPOINTMENTS(appointment_id),
    FOREIGN KEY (room_id) REFERENCES ROOMS(room_id)
);

-- إنشاء الفهارس لتحسين الأداء
CREATE INDEX IX_PATIENTS_Name ON PATIENTS(first_name, last_name);
CREATE INDEX IX_PATIENTS_Phone ON PATIENTS(phone);
CREATE INDEX IX_APPOINTMENTS_Date ON APPOINTMENTS(appointment_datetime);
CREATE INDEX IX_APPOINTMENTS_Status ON APPOINTMENTS(status);
CREATE INDEX IX_MEDICAL_RECORDS_Date ON MEDICAL_RECORDS(visit_date);
CREATE INDEX IX_BILLING_Date ON BILLING(bill_date);
CREATE INDEX IX_BILLING_Status ON BILLING(payment_status);

-- إدراج بيانات تجريبية

-- إدراج أطباء
INSERT INTO DOCTORS (first_name, last_name, specialty, license_number, phone, email, work_start_time, work_end_time, work_days, consultation_fee) VALUES
('Ahmed', 'Benali', 'Médecine Générale', 'DOC001', '**********', '<EMAIL>', '08:00', '17:00', 'Lundi,Mardi,Mercredi,Jeudi,Vendredi', 80.00),
('Fatima', 'Zahra', 'Cardiologie', 'DOC002', '**********', '<EMAIL>', '09:00', '16:00', 'Lundi,Mercredi,Vendredi', 120.00),
('Mohamed', 'Alami', 'Pédiatrie', 'DOC003', '**********', '<EMAIL>', '08:30', '17:30', 'Mardi,Jeudi,Samedi', 90.00),
('Aicha', 'Idrissi', 'Gynécologie', 'DOC004', '**********', '<EMAIL>', '09:00', '15:00', 'Lundi,Mardi,Jeudi,Vendredi', 100.00);

-- إدراج مرضى
INSERT INTO PATIENTS (first_name, last_name, birth_date, gender, phone, email, address, emergency_contact_name, emergency_contact_phone, blood_type, allergies, medical_history) VALUES
('Omar', 'Tazi', '1985-03-15', 'Homme', '**********', '<EMAIL>', '123 Rue Mohammed V, Casablanca', 'Khadija Tazi', '**********', 'A+', 'Aucune allergie connue', 'Hypertension légère'),
('Zineb', 'Fassi', '1990-07-22', 'Femme', '**********', '<EMAIL>', '456 Avenue Hassan II, Rabat', 'Youssef Fassi', '**********', 'O+', 'Allergie aux arachides', 'Diabète type 2'),
('Karim', 'Benjelloun', '1978-11-08', 'Homme', '**********', '<EMAIL>', '789 Boulevard Zerktouni, Casablanca', 'Samira Benjelloun', '**********', 'B+', 'Allergie à la pénicilline', 'Asthme'),
('Laila', 'Chraibi', '1995-05-30', 'Femme', '**********', '<EMAIL>', '321 Rue Allal Ben Abdellah, Fès', 'Hassan Chraibi', '**********', 'AB+', 'Aucune allergie connue', 'Aucun antécédent'),
('Rachid', 'Ouali', '1982-12-12', 'Homme', '**********', '<EMAIL>', '654 Avenue Mohammed VI, Marrakech', 'Nadia Ouali', '**********', 'O-', 'Allergie aux fruits de mer', 'Cholestérol élevé'),
('Salma', 'Kettani', '1988-09-18', 'Femme', '0612345688', '<EMAIL>', '987 Rue Ibn Sina, Agadir', 'Khalid Kettani', '0612345689', 'A-', 'Aucune allergie connue', 'Migraine chronique'),
('Youssef', 'Berrada', '1975-04-25', 'Homme', '0612345690', '<EMAIL>', '147 Boulevard Moulay Youssef, Tanger', 'Amina Berrada', '0612345691', 'B-', 'Allergie au latex', 'Arthrite'),
('Nour', 'Alaoui', '1992-08-14', 'Femme', '0612345692', '<EMAIL>', '258 Rue Al Massira, Oujda', 'Mehdi Alaoui', '0612345693', 'AB-', 'Allergie aux antibiotiques', 'Aucun antécédent'),
('Hamza', 'Semlali', '1987-01-03', 'Homme', '0612345694', '<EMAIL>', '369 Avenue Lalla Yacout, Kenitra', 'Fatima Semlali', '0612345695', 'A+', 'Aucune allergie connue', 'Ulcère gastrique'),
('Meryem', 'Hajji', '1993-06-27', 'Femme', '0612345696', '<EMAIL>', '741 Rue Prince Héritier, Tétouan', 'Omar Hajji', '0612345697', 'O+', 'Allergie au pollen', 'Anémie');

-- إدراج غرف
INSERT INTO ROOMS (room_number, room_type, status, equipment) VALUES
('101', 'Consultation', 'Disponible', 'Bureau médical, chaise d''examen, tensiomètre'),
('102', 'Consultation', 'Disponible', 'Bureau médical, chaise d''examen, stéthoscope'),
('103', 'Examen', 'Disponible', 'Table d''examen, équipement de diagnostic'),
('104', 'Urgence', 'Disponible', 'Équipement d''urgence, défibrillateur'),
('105', 'Pédiatrie', 'Disponible', 'Équipement pédiatrique, jouets'),
('201', 'Cardiologie', 'Disponible', 'ECG, échographe cardiaque'),
('202', 'Gynécologie', 'Disponible', 'Table gynécologique, échographe'),
('203', 'Attente', 'Disponible', 'Chaises, magazines, télévision');

-- إدراج مواعيد
INSERT INTO APPOINTMENTS (patient_id, doctor_id, appointment_datetime, status, appointment_type, notes, fee) VALUES
(1, 1, '2024-01-15 09:00:00', 'Terminé', 'Consultation', 'Consultation de routine', 80.00),
(2, 2, '2024-01-15 10:30:00', 'Terminé', 'Consultation', 'Suivi cardiologique', 120.00),
(3, 3, '2024-01-15 14:00:00', 'Réservé', 'Consultation', 'Consultation pédiatrique', 90.00),
(4, 4, '2024-01-16 09:30:00', 'Réservé', 'Suivi', 'Suivi gynécologique', 100.00),
(5, 1, '2024-01-16 11:00:00', 'Réservé', 'Consultation', 'Contrôle cholestérol', 80.00),
(6, 2, '2024-01-17 15:00:00', 'Réservé', 'Consultation', 'Consultation cardiologique', 120.00),
(7, 1, '2024-01-17 16:30:00', 'Réservé', 'Suivi', 'Suivi arthrite', 80.00),
(8, 3, '2024-01-18 10:00:00', 'Réservé', 'Consultation', 'Consultation générale', 90.00);

-- إدراج أدوية
INSERT INTO MEDICATIONS (medication_name, dosage_form, strength, manufacturer, description, unit_price, stock_quantity, expiry_date) VALUES
('Paracétamol', 'Comprimés', '500mg', 'Pharma Maroc', 'Analgésique et antipyrétique', 0.50, 1000, '2025-12-31'),
('Amoxicilline', 'Gélules', '250mg', 'Antibio SA', 'Antibiotique à large spectre', 1.20, 500, '2025-06-30'),
('Ibuprofène', 'Comprimés', '400mg', 'Anti-Inflamm Ltd', 'Anti-inflammatoire non stéroïdien', 0.80, 750, '2025-09-15'),
('Oméprazole', 'Gélules', '20mg', 'Gastro Pharma', 'Inhibiteur de la pompe à protons', 2.50, 300, '2025-03-20'),
('Metformine', 'Comprimés', '850mg', 'Diabète Care', 'Antidiabétique', 1.80, 400, '2025-11-10');

-- إدراج فحوصات مخبرية
INSERT INTO LAB_TESTS (test_name, test_category, test_cost, normal_range, description) VALUES
('Numération Formule Sanguine', 'Hématologie', 25.00, 'Globules rouges: 4.5-5.5 M/μL', 'Analyse complète du sang'),
('Glycémie à jeun', 'Biochimie', 15.00, '70-100 mg/dL', 'Mesure du taux de glucose sanguin'),
('Cholestérol total', 'Biochimie', 20.00, '<200 mg/dL', 'Mesure du cholestérol total'),
('Créatinine', 'Biochimie', 18.00, '0.6-1.2 mg/dL', 'Fonction rénale'),
('TSH', 'Endocrinologie', 30.00, '0.4-4.0 mIU/L', 'Hormone thyréostimulante'),
('Radiographie thoracique', 'Imagerie', 50.00, 'Normal', 'Examen radiologique du thorax'),
('Échographie abdominale', 'Imagerie', 80.00, 'Normal', 'Examen échographique de l''abdomen');

GO
