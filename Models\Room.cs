using System.ComponentModel.DataAnnotations;

namespace CleverMC.Models
{
    public class Room
    {
        [Key]
        public int RoomId { get; set; }

        [Required]
        [StringLength(10)]
        public string RoomNumber { get; set; } = string.Empty;

        [Required]
        [StringLength(50)]
        public string RoomType { get; set; } = string.Empty;

        [StringLength(20)]
        public string Status { get; set; } = "Disponible";

        public string? Equipment { get; set; }

        public bool IsAvailable { get; set; } = true;

        // Navigation properties
        public virtual ICollection<AppointmentRoom> AppointmentRooms { get; set; } = new List<AppointmentRoom>();
    }
}
