using CleverMC.Data;
using CleverMC.Models;

namespace CleverMC.Forms
{
    public partial class PatientAddEditForm : Form
    {
        private MedicalClinicDbContext _context;
        private int? _patientId;
        private Patient? _patient;

        // Controls
        private TextBox firstNameTextBox;
        private TextBox lastNameTextBox;
        private DateTimePicker birthDatePicker;
        private ComboBox genderComboBox;
        private TextBox phoneTextBox;
        private TextBox emailTextBox;
        private TextBox addressTextBox;
        private TextBox emergencyContactNameTextBox;
        private TextBox emergencyContactPhoneTextBox;
        private ComboBox bloodTypeComboBox;
        private TextBox allergiesTextBox;
        private TextBox medicalHistoryTextBox;
        private Button saveButton;
        private Button cancelButton;

        public PatientAddEditForm(MedicalClinicDbContext context, int? patientId = null)
        {
            _context = context;
            _patientId = patientId;
            InitializeComponent();
            SetupUI();
            LoadPatientData();
        }

        private void SetupUI()
        {
            this.Text = _patientId.HasValue ? "Modifier Patient" : "Ajouter Patient";
            this.Size = new Size(800, 700);
            this.StartPosition = FormStartPosition.CenterParent;
            this.BackColor = Color.FromArgb(248, 250, 252);
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;

            CreateControls();
            LayoutControls();
        }

        private void CreateControls()
        {
            // Personal Information
            firstNameTextBox = new TextBox { Font = new Font("Segoe UI", 11), Size = new Size(200, 30) };
            lastNameTextBox = new TextBox { Font = new Font("Segoe UI", 11), Size = new Size(200, 30) };
            birthDatePicker = new DateTimePicker { Font = new Font("Segoe UI", 11), Size = new Size(200, 30), Format = DateTimePickerFormat.Short };
            
            genderComboBox = new ComboBox 
            { 
                Font = new Font("Segoe UI", 11), 
                Size = new Size(200, 30),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            genderComboBox.Items.AddRange(new[] { "Homme", "Femme" });

            phoneTextBox = new TextBox { Font = new Font("Segoe UI", 11), Size = new Size(200, 30) };
            emailTextBox = new TextBox { Font = new Font("Segoe UI", 11), Size = new Size(200, 30) };
            addressTextBox = new TextBox { Font = new Font("Segoe UI", 11), Size = new Size(420, 60), Multiline = true };

            // Emergency Contact
            emergencyContactNameTextBox = new TextBox { Font = new Font("Segoe UI", 11), Size = new Size(200, 30) };
            emergencyContactPhoneTextBox = new TextBox { Font = new Font("Segoe UI", 11), Size = new Size(200, 30) };

            // Medical Information
            bloodTypeComboBox = new ComboBox 
            { 
                Font = new Font("Segoe UI", 11), 
                Size = new Size(200, 30),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            bloodTypeComboBox.Items.AddRange(new[] { "A+", "A-", "B+", "B-", "AB+", "AB-", "O+", "O-" });

            allergiesTextBox = new TextBox { Font = new Font("Segoe UI", 11), Size = new Size(420, 60), Multiline = true };
            medicalHistoryTextBox = new TextBox { Font = new Font("Segoe UI", 11), Size = new Size(420, 80), Multiline = true };

            // Buttons
            saveButton = new Button
            {
                Text = "Enregistrer",
                Font = new Font("Segoe UI", 11, FontStyle.Bold),
                Size = new Size(120, 40),
                BackColor = Color.FromArgb(16, 185, 129),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand
            };
            saveButton.Click += SaveButton_Click;

            cancelButton = new Button
            {
                Text = "Annuler",
                Font = new Font("Segoe UI", 11),
                Size = new Size(120, 40),
                BackColor = Color.FromArgb(107, 114, 128),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand
            };
            cancelButton.Click += (s, e) => this.DialogResult = DialogResult.Cancel;
        }

        private void LayoutControls()
        {
            var mainPanel = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(30),
                BackColor = Color.White,
                Margin = new Padding(20)
            };

            int yPos = 20;

            // Personal Information Section
            var personalInfoLabel = CreateSectionLabel("Informations Personnelles", yPos);
            mainPanel.Controls.Add(personalInfoLabel);
            yPos += 40;

            mainPanel.Controls.Add(CreateLabel("Prénom *:", 20, yPos));
            firstNameTextBox.Location = new Point(150, yPos);
            mainPanel.Controls.Add(firstNameTextBox);

            mainPanel.Controls.Add(CreateLabel("Nom *:", 370, yPos));
            lastNameTextBox.Location = new Point(500, yPos);
            mainPanel.Controls.Add(lastNameTextBox);
            yPos += 40;

            mainPanel.Controls.Add(CreateLabel("Date de Naissance *:", 20, yPos));
            birthDatePicker.Location = new Point(150, yPos);
            mainPanel.Controls.Add(birthDatePicker);

            mainPanel.Controls.Add(CreateLabel("Sexe:", 370, yPos));
            genderComboBox.Location = new Point(500, yPos);
            mainPanel.Controls.Add(genderComboBox);
            yPos += 40;

            mainPanel.Controls.Add(CreateLabel("Téléphone:", 20, yPos));
            phoneTextBox.Location = new Point(150, yPos);
            mainPanel.Controls.Add(phoneTextBox);

            mainPanel.Controls.Add(CreateLabel("Email:", 370, yPos));
            emailTextBox.Location = new Point(500, yPos);
            mainPanel.Controls.Add(emailTextBox);
            yPos += 40;

            mainPanel.Controls.Add(CreateLabel("Adresse:", 20, yPos));
            addressTextBox.Location = new Point(150, yPos);
            mainPanel.Controls.Add(addressTextBox);
            yPos += 80;

            // Emergency Contact Section
            var emergencyLabel = CreateSectionLabel("Contact d'Urgence", yPos);
            mainPanel.Controls.Add(emergencyLabel);
            yPos += 40;

            mainPanel.Controls.Add(CreateLabel("Nom:", 20, yPos));
            emergencyContactNameTextBox.Location = new Point(150, yPos);
            mainPanel.Controls.Add(emergencyContactNameTextBox);

            mainPanel.Controls.Add(CreateLabel("Téléphone:", 370, yPos));
            emergencyContactPhoneTextBox.Location = new Point(500, yPos);
            mainPanel.Controls.Add(emergencyContactPhoneTextBox);
            yPos += 40;

            // Medical Information Section
            var medicalLabel = CreateSectionLabel("Informations Médicales", yPos);
            mainPanel.Controls.Add(medicalLabel);
            yPos += 40;

            mainPanel.Controls.Add(CreateLabel("Groupe Sanguin:", 20, yPos));
            bloodTypeComboBox.Location = new Point(150, yPos);
            mainPanel.Controls.Add(bloodTypeComboBox);
            yPos += 40;

            mainPanel.Controls.Add(CreateLabel("Allergies:", 20, yPos));
            allergiesTextBox.Location = new Point(150, yPos);
            mainPanel.Controls.Add(allergiesTextBox);
            yPos += 80;

            mainPanel.Controls.Add(CreateLabel("Antécédents Médicaux:", 20, yPos));
            medicalHistoryTextBox.Location = new Point(150, yPos);
            mainPanel.Controls.Add(medicalHistoryTextBox);
            yPos += 100;

            // Buttons
            saveButton.Location = new Point(450, yPos);
            cancelButton.Location = new Point(580, yPos);
            mainPanel.Controls.AddRange(new Control[] { saveButton, cancelButton });

            this.Controls.Add(mainPanel);
        }

        private Label CreateSectionLabel(string text, int y)
        {
            return new Label
            {
                Text = text,
                Font = new Font("Segoe UI", 14, FontStyle.Bold),
                ForeColor = Color.FromArgb(51, 65, 85),
                AutoSize = true,
                Location = new Point(20, y)
            };
        }

        private Label CreateLabel(string text, int x, int y)
        {
            return new Label
            {
                Text = text,
                Font = new Font("Segoe UI", 11),
                ForeColor = Color.FromArgb(75, 85, 99),
                AutoSize = true,
                Location = new Point(x, y + 5)
            };
        }

        private void LoadPatientData()
        {
            if (_patientId.HasValue)
            {
                _patient = _context.Patients.Find(_patientId.Value);
                if (_patient != null)
                {
                    firstNameTextBox.Text = _patient.FirstName;
                    lastNameTextBox.Text = _patient.LastName;
                    birthDatePicker.Value = _patient.BirthDate;
                    genderComboBox.Text = _patient.Gender;
                    phoneTextBox.Text = _patient.Phone;
                    emailTextBox.Text = _patient.Email;
                    addressTextBox.Text = _patient.Address;
                    emergencyContactNameTextBox.Text = _patient.EmergencyContactName;
                    emergencyContactPhoneTextBox.Text = _patient.EmergencyContactPhone;
                    bloodTypeComboBox.Text = _patient.BloodType;
                    allergiesTextBox.Text = _patient.Allergies;
                    medicalHistoryTextBox.Text = _patient.MedicalHistory;
                }
            }
        }

        private void SaveButton_Click(object sender, EventArgs e)
        {
            if (!ValidateInput()) return;

            try
            {
                if (_patient == null)
                {
                    _patient = new Patient();
                    _context.Patients.Add(_patient);
                }

                _patient.FirstName = firstNameTextBox.Text.Trim();
                _patient.LastName = lastNameTextBox.Text.Trim();
                _patient.BirthDate = birthDatePicker.Value.Date;
                _patient.Gender = genderComboBox.Text;
                _patient.Phone = phoneTextBox.Text.Trim();
                _patient.Email = emailTextBox.Text.Trim();
                _patient.Address = addressTextBox.Text.Trim();
                _patient.EmergencyContactName = emergencyContactNameTextBox.Text.Trim();
                _patient.EmergencyContactPhone = emergencyContactPhoneTextBox.Text.Trim();
                _patient.BloodType = bloodTypeComboBox.Text;
                _patient.Allergies = allergiesTextBox.Text.Trim();
                _patient.MedicalHistory = medicalHistoryTextBox.Text.Trim();

                _context.SaveChanges();
                this.DialogResult = DialogResult.OK;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Erreur lors de l'enregistrement: {ex.Message}", "Erreur",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private bool ValidateInput()
        {
            if (string.IsNullOrWhiteSpace(firstNameTextBox.Text))
            {
                MessageBox.Show("Le prénom est obligatoire.", "Validation", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                firstNameTextBox.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(lastNameTextBox.Text))
            {
                MessageBox.Show("Le nom est obligatoire.", "Validation", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                lastNameTextBox.Focus();
                return false;
            }

            if (birthDatePicker.Value > DateTime.Now)
            {
                MessageBox.Show("La date de naissance ne peut pas être dans le futur.", "Validation", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                birthDatePicker.Focus();
                return false;
            }

            return true;
        }
    }
}
