using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace CleverMC.Models
{
    public class Billing
    {
        [Key]
        public int BillId { get; set; }

        [Required]
        public int PatientId { get; set; }

        public int? AppointmentId { get; set; }

        [Column(TypeName = "decimal(10,2)")]
        public decimal ConsultationFee { get; set; } = 0;

        [Column(TypeName = "decimal(10,2)")]
        public decimal MedicationCost { get; set; } = 0;

        [Column(TypeName = "decimal(10,2)")]
        public decimal TestCost { get; set; } = 0;

        [Column(TypeName = "decimal(10,2)")]
        public decimal OtherCharges { get; set; } = 0;

        [Column(TypeName = "decimal(10,2)")]
        public decimal PaidAmount { get; set; } = 0;

        [StringLength(50)]
        public string? PaymentMethod { get; set; }

        [StringLength(20)]
        public string PaymentStatus { get; set; } = "Non payé";

        public DateTime BillDate { get; set; } = DateTime.Now;

        public DateTime? PaymentDate { get; set; }

        // Navigation properties
        [ForeignKey("PatientId")]
        public virtual Patient Patient { get; set; } = null!;

        [ForeignKey("AppointmentId")]
        public virtual Appointment? Appointment { get; set; }

        // Computed properties
        [NotMapped]
        public decimal TotalAmount => ConsultationFee + MedicationCost + TestCost + OtherCharges;

        [NotMapped]
        public decimal Balance => TotalAmount - PaidAmount;
    }
}
