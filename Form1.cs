using CleverMC.Data;
using CleverMC.Forms;
using Microsoft.EntityFrameworkCore;

namespace CleverMC
{
    public partial class MainForm : Form
    {
        private MedicalClinicDbContext _context = null!;
        private Panel sidebarPanel = null!;
        private Panel mainContentPanel = null!;
        private Panel dashboardPanel = null!;

        public MainForm()
        {
            InitializeComponent();
            InitializeDatabase();
            SetupModernUI();
            LoadDashboardData();
        }

        private void InitializeDatabase()
        {
            var options = new DbContextOptionsBuilder<MedicalClinicDbContext>()
                .UseSqlServer("Server=.;Database=MedicalClinicDB;Trusted_Connection=true;TrustServerCertificate=true;")
                .Options;

            _context = new MedicalClinicDbContext(options);
        }

        private void SetupModernUI()
        {
            // Configure main form
            this.Text = "CleverMC - Gestion de Clinique Médicale";
            this.Size = new Size(1400, 900);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.BackColor = Color.FromArgb(240, 244, 248);
            this.WindowState = FormWindowState.Maximized;

            CreateSidebar();
            CreateMainContent();
            CreateDashboard();
        }

        private void CreateSidebar()
        {
            sidebarPanel = new Panel
            {
                Dock = DockStyle.Left,
                Width = 250,
                BackColor = Color.FromArgb(139, 69, 19), // Marron foncé
                Padding = new Padding(0, 20, 0, 0)
            };

            // Logo/Title
            var titleLabel = new Label
            {
                Text = "CleverMC",
                Font = new Font("Segoe UI", 18, FontStyle.Bold),
                ForeColor = Color.White,
                AutoSize = false,
                Size = new Size(250, 60),
                TextAlign = ContentAlignment.MiddleCenter,
                Dock = DockStyle.Top
            };

            sidebarPanel.Controls.Add(titleLabel);

            // Menu buttons
            var menuItems = new[]
            {
                new { Text = "Tableau de Bord", Icon = "📊" },
                new { Text = "Patients", Icon = "👥" },
                new { Text = "Médecins", Icon = "👨‍⚕️" },
                new { Text = "Rendez-vous", Icon = "📅" },
                new { Text = "Dossiers Médicaux", Icon = "📋" },
                new { Text = "Facturation", Icon = "💰" },
                new { Text = "Médicaments", Icon = "💊" },
                new { Text = "Rapports", Icon = "📈" },
                new { Text = "Paramètres", Icon = "⚙️" }
            };

            int yPosition = 80;
            foreach (var item in menuItems)
            {
                var button = CreateMenuButton(item.Text, item.Icon, yPosition);
                sidebarPanel.Controls.Add(button);
                yPosition += 55;
            }

            this.Controls.Add(sidebarPanel);
        }

        private Button CreateMenuButton(string text, string icon, int yPosition)
        {
            var button = new Button
            {
                Text = $"{icon}  {text}",
                Font = new Font("Segoe UI", 11),
                ForeColor = Color.White,
                BackColor = Color.Transparent,
                FlatStyle = FlatStyle.Flat,
                TextAlign = ContentAlignment.MiddleLeft,
                Size = new Size(240, 45),
                Location = new Point(5, yPosition),
                Cursor = Cursors.Hand
            };

            button.FlatAppearance.BorderSize = 0;
            button.FlatAppearance.MouseOverBackColor = Color.FromArgb(160, 90, 40);

            button.Click += (s, e) => HandleMenuClick(text);

            return button;
        }

        private void CreateMainContent()
        {
            mainContentPanel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.FromArgb(248, 250, 252),
                Padding = new Padding(20)
            };

            this.Controls.Add(mainContentPanel);
        }

        private void CreateDashboard()
        {
            dashboardPanel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.Transparent
            };

            // Header
            var headerPanel = new Panel
            {
                Dock = DockStyle.Top,
                Height = 80,
                BackColor = Color.Transparent
            };

            var welcomeLabel = new Label
            {
                Text = "Tableau de Bord",
                Font = new Font("Segoe UI", 24, FontStyle.Bold),
                ForeColor = Color.FromArgb(51, 65, 85),
                AutoSize = true,
                Location = new Point(0, 20)
            };

            var dateLabel = new Label
            {
                Text = DateTime.Now.ToString("dddd, dd MMMM yyyy", new System.Globalization.CultureInfo("fr-FR")),
                Font = new Font("Segoe UI", 12),
                ForeColor = Color.FromArgb(100, 116, 139),
                AutoSize = true,
                Location = new Point(0, 55)
            };

            headerPanel.Controls.AddRange(new Control[] { welcomeLabel, dateLabel });
            dashboardPanel.Controls.Add(headerPanel);

            CreateStatsCards();
            CreateCharts();

            mainContentPanel.Controls.Add(dashboardPanel);
        }

        private void CreateStatsCards()
        {
            var statsPanel = new Panel
            {
                Dock = DockStyle.Top,
                Height = 150,
                BackColor = Color.Transparent,
                Padding = new Padding(0, 20, 0, 20)
            };

            var cards = new[]
            {
                new { Title = "Patients Totaux", Value = "1,234", Icon = "👥", Color = Color.FromArgb(59, 130, 246) },
                new { Title = "RDV Aujourd'hui", Value = "28", Icon = "📅", Color = Color.FromArgb(16, 185, 129) },
                new { Title = "Revenus du Mois", Value = "€15,420", Icon = "💰", Color = Color.FromArgb(245, 158, 11) },
                new { Title = "Médecins Actifs", Value = "12", Icon = "👨‍⚕️", Color = Color.FromArgb(239, 68, 68) }
            };

            int cardWidth = 280;
            int cardSpacing = 20;

            for (int i = 0; i < cards.Length; i++)
            {
                var card = CreateStatsCard(cards[i].Title, cards[i].Value, cards[i].Icon, cards[i].Color);
                card.Location = new Point(i * (cardWidth + cardSpacing), 0);
                card.Size = new Size(cardWidth, 110);
                statsPanel.Controls.Add(card);
            }

            dashboardPanel.Controls.Add(statsPanel);
        }

        private Panel CreateStatsCard(string title, string value, string icon, Color accentColor)
        {
            var card = new Panel
            {
                BackColor = Color.White,
                Padding = new Padding(20),
                Margin = new Padding(0, 0, 20, 0)
            };

            var iconLabel = new Label
            {
                Text = icon,
                Font = new Font("Segoe UI", 24),
                ForeColor = accentColor,
                AutoSize = true,
                Location = new Point(20, 15)
            };

            var valueLabel = new Label
            {
                Text = value,
                Font = new Font("Segoe UI", 20, FontStyle.Bold),
                ForeColor = Color.FromArgb(51, 65, 85),
                AutoSize = true,
                Location = new Point(80, 15)
            };

            var titleLabel = new Label
            {
                Text = title,
                Font = new Font("Segoe UI", 11),
                ForeColor = Color.FromArgb(100, 116, 139),
                AutoSize = true,
                Location = new Point(80, 50)
            };

            card.Controls.AddRange(new Control[] { iconLabel, valueLabel, titleLabel });
            return card;
        }

        private void CreateCharts()
        {
            var chartsPanel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.Transparent,
                Padding = new Padding(0, 20, 0, 0)
            };

            // Simple chart placeholder
            var chartLabel = new Label
            {
                Text = "📊 Graphiques des Rendez-vous et Revenus",
                Font = new Font("Segoe UI", 16, FontStyle.Bold),
                ForeColor = Color.FromArgb(51, 65, 85),
                AutoSize = true,
                Location = new Point(20, 20)
            };

            chartsPanel.Controls.Add(chartLabel);
            dashboardPanel.Controls.Add(chartsPanel);
        }

        private void HandleMenuClick(string menuText)
        {
            // Clear current content
            mainContentPanel.Controls.Clear();

            switch (menuText)
            {
                case "Tableau de Bord":
                    mainContentPanel.Controls.Add(dashboardPanel);
                    break;
                case "Patients":
                    ShowPatientsForm();
                    break;
                case "Médecins":
                    ShowDoctorsForm();
                    break;
                case "Rendez-vous":
                    ShowAppointmentsForm();
                    break;
                case "Facturation":
                    ShowBillingForm();
                    break;
                default:
                    MessageBox.Show($"Module '{menuText}' en cours de développement.", "Information",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    mainContentPanel.Controls.Add(dashboardPanel);
                    break;
            }
        }

        private void LoadDashboardData()
        {
            try
            {
                // This will be implemented to load real data from database
                // For now, we're using sample data
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Erreur lors du chargement des données: {ex.Message}", "Erreur",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ShowPatientsForm()
        {
            var patientForm = new PatientManagementForm(_context)
            {
                Dock = DockStyle.Fill,
                TopLevel = false,
                FormBorderStyle = FormBorderStyle.None
            };

            mainContentPanel.Controls.Add(patientForm);
            patientForm.Show();
        }

        private void ShowDoctorsForm()
        {
            var label = new Label
            {
                Text = "Gestion des Médecins - En cours de développement",
                Font = new Font("Segoe UI", 16),
                AutoSize = true,
                Location = new Point(50, 50)
            };
            mainContentPanel.Controls.Add(label);
        }

        private void ShowAppointmentsForm()
        {
            var label = new Label
            {
                Text = "Gestion des Rendez-vous - En cours de développement",
                Font = new Font("Segoe UI", 16),
                AutoSize = true,
                Location = new Point(50, 50)
            };
            mainContentPanel.Controls.Add(label);
        }

        private void ShowBillingForm()
        {
            var label = new Label
            {
                Text = "Gestion de la Facturation - En cours de développement",
                Font = new Font("Segoe UI", 16),
                AutoSize = true,
                Location = new Point(50, 50)
            };
            mainContentPanel.Controls.Add(label);
        }

        protected override void OnFormClosed(FormClosedEventArgs e)
        {
            _context?.Dispose();
            base.OnFormClosed(e);
        }
    }
}
