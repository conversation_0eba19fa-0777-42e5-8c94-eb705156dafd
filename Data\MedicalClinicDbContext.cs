using Microsoft.EntityFrameworkCore;
using CleverMC.Models;

namespace CleverMC.Data
{
    public class MedicalClinicDbContext : DbContext
    {
        public MedicalClinicDbContext(DbContextOptions<MedicalClinicDbContext> options) : base(options)
        {
        }

        public DbSet<Patient> Patients { get; set; }
        public DbSet<Doctor> Doctors { get; set; }
        public DbSet<Staff> Staff { get; set; }
        public DbSet<Room> Rooms { get; set; }
        public DbSet<Appointment> Appointments { get; set; }
        public DbSet<MedicalRecord> MedicalRecords { get; set; }
        public DbSet<Medication> Medications { get; set; }
        public DbSet<Prescription> Prescriptions { get; set; }
        public DbSet<Inventory> Inventories { get; set; }
        public DbSet<Insurance> Insurances { get; set; }
        public DbSet<LabTest> LabTests { get; set; }
        public DbSet<PatientTest> PatientTests { get; set; }
        public DbSet<Billing> Billings { get; set; }
        public DbSet<AppointmentRoom> AppointmentRooms { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // Configure table names to match database
            modelBuilder.Entity<Patient>().ToTable("PATIENTS");
            modelBuilder.Entity<Doctor>().ToTable("DOCTORS");
            modelBuilder.Entity<Staff>().ToTable("STAFF");
            modelBuilder.Entity<Room>().ToTable("ROOMS");
            modelBuilder.Entity<Appointment>().ToTable("APPOINTMENTS");
            modelBuilder.Entity<MedicalRecord>().ToTable("MEDICAL_RECORDS");
            modelBuilder.Entity<Medication>().ToTable("MEDICATIONS");
            modelBuilder.Entity<Prescription>().ToTable("PRESCRIPTIONS");
            modelBuilder.Entity<Inventory>().ToTable("INVENTORY");
            modelBuilder.Entity<Insurance>().ToTable("INSURANCE");
            modelBuilder.Entity<LabTest>().ToTable("LAB_TESTS");
            modelBuilder.Entity<PatientTest>().ToTable("PATIENT_TESTS");
            modelBuilder.Entity<Billing>().ToTable("BILLING");
            modelBuilder.Entity<AppointmentRoom>().ToTable("APPOINTMENT_ROOMS");

            // Configure column names to match database
            modelBuilder.Entity<Patient>(entity =>
            {
                entity.Property(e => e.PatientId).HasColumnName("patient_id");
                entity.Property(e => e.FirstName).HasColumnName("first_name");
                entity.Property(e => e.LastName).HasColumnName("last_name");
                entity.Property(e => e.BirthDate).HasColumnName("birth_date");
                entity.Property(e => e.Gender).HasColumnName("gender");
                entity.Property(e => e.Phone).HasColumnName("phone");
                entity.Property(e => e.Email).HasColumnName("email");
                entity.Property(e => e.Address).HasColumnName("address");
                entity.Property(e => e.EmergencyContactName).HasColumnName("emergency_contact_name");
                entity.Property(e => e.EmergencyContactPhone).HasColumnName("emergency_contact_phone");
                entity.Property(e => e.BloodType).HasColumnName("blood_type");
                entity.Property(e => e.Allergies).HasColumnName("allergies");
                entity.Property(e => e.MedicalHistory).HasColumnName("medical_history");
                entity.Property(e => e.RegistrationDate).HasColumnName("registration_date");
                entity.Property(e => e.IsActive).HasColumnName("is_active");
            });

            modelBuilder.Entity<Doctor>(entity =>
            {
                entity.Property(e => e.DoctorId).HasColumnName("doctor_id");
                entity.Property(e => e.FirstName).HasColumnName("first_name");
                entity.Property(e => e.LastName).HasColumnName("last_name");
                entity.Property(e => e.Specialty).HasColumnName("specialty");
                entity.Property(e => e.LicenseNumber).HasColumnName("license_number");
                entity.Property(e => e.Phone).HasColumnName("phone");
                entity.Property(e => e.Email).HasColumnName("email");
                entity.Property(e => e.WorkStartTime).HasColumnName("work_start_time");
                entity.Property(e => e.WorkEndTime).HasColumnName("work_end_time");
                entity.Property(e => e.WorkDays).HasColumnName("work_days");
                entity.Property(e => e.ConsultationFee).HasColumnName("consultation_fee");
                entity.Property(e => e.HireDate).HasColumnName("hire_date");
                entity.Property(e => e.IsActive).HasColumnName("is_active");
            });

            // Configure constraints
            modelBuilder.Entity<Patient>()
                .HasCheckConstraint("CK_Patient_Gender", "gender IN ('Homme', 'Femme')");

            modelBuilder.Entity<Patient>()
                .HasCheckConstraint("CK_Patient_BloodType", "blood_type IN ('A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-')");

            modelBuilder.Entity<Appointment>()
                .HasCheckConstraint("CK_Appointment_Status", "status IN ('Réservé', 'Terminé', 'Annulé', 'Absent')");

            modelBuilder.Entity<Appointment>()
                .HasCheckConstraint("CK_Appointment_Type", "appointment_type IN ('Consultation', 'Suivi', 'Urgence', 'Examen périodique')");

            // Configure unique constraints
            modelBuilder.Entity<Doctor>()
                .HasIndex(d => d.LicenseNumber)
                .IsUnique();

            modelBuilder.Entity<Room>()
                .HasIndex(r => r.RoomNumber)
                .IsUnique();
        }

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            if (!optionsBuilder.IsConfigured)
            {
                optionsBuilder.UseSqlServer("Server=.;Database=MedicalClinicDB;Trusted_Connection=true;TrustServerCertificate=true;");
            }
        }
    }
}
