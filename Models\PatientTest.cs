using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace CleverMC.Models
{
    public class PatientTest
    {
        [Key]
        public int PatientTestId { get; set; }

        [Required]
        public int PatientId { get; set; }

        public int? RecordId { get; set; }

        [Required]
        public int TestId { get; set; }

        [StringLength(500)]
        public string? TestResult { get; set; }

        public DateTime TestDate { get; set; } = DateTime.Now;

        [StringLength(20)]
        public string Status { get; set; } = "Demandé";

        public string? TechnicianNotes { get; set; }

        [Column(TypeName = "decimal(10,2)")]
        public decimal? Cost { get; set; }

        // Navigation properties
        [ForeignKey("PatientId")]
        public virtual Patient Patient { get; set; } = null!;

        [ForeignKey("RecordId")]
        public virtual MedicalRecord? MedicalRecord { get; set; }

        [ForeignKey("TestId")]
        public virtual LabTest LabTest { get; set; } = null!;
    }
}
