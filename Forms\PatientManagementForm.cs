using CleverMC.Data;
using CleverMC.Models;
using Microsoft.EntityFrameworkCore;

namespace CleverMC.Forms
{
    public partial class PatientManagementForm : Form
    {
        private MedicalClinicDbContext _context;
        private DataGridView patientsGrid;
        private Panel searchPanel;
        private TextBox searchTextBox;
        private Button addButton;
        private Button editButton;
        private Button deleteButton;

        public PatientManagementForm(MedicalClinicDbContext context)
        {
            _context = context;
            InitializeComponent();
            SetupUI();
            LoadPatients();
        }

        private void SetupUI()
        {
            this.Text = "Gestion des Patients";
            this.Size = new Size(1200, 800);
            this.BackColor = Color.FromArgb(248, 250, 252);

            CreateSearchPanel();
            CreateDataGrid();
            CreateActionButtons();
        }

        private void CreateSearchPanel()
        {
            searchPanel = new Panel
            {
                Dock = DockStyle.Top,
                Height = 80,
                BackColor = Color.White,
                Padding = new Padding(20)
            };

            var titleLabel = new Label
            {
                Text = "Gestion des Patients",
                Font = new Font("Segoe UI", 18, FontStyle.Bold),
                ForeColor = Color.FromArgb(51, 65, 85),
                AutoSize = true,
                Location = new Point(20, 15)
            };

            searchTextBox = new TextBox
            {
                PlaceholderText = "Rechercher un patient...",
                Font = new Font("Segoe UI", 11),
                Size = new Size(300, 30),
                Location = new Point(20, 45)
            };

            searchTextBox.TextChanged += SearchTextBox_TextChanged;

            searchPanel.Controls.AddRange(new Control[] { titleLabel, searchTextBox });
            this.Controls.Add(searchPanel);
        }

        private void CreateDataGrid()
        {
            patientsGrid = new DataGridView
            {
                Dock = DockStyle.Fill,
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.None,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                ReadOnly = true,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                MultiSelect = false,
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill,
                RowHeadersVisible = false,
                Font = new Font("Segoe UI", 10)
            };

            // Configure columns
            patientsGrid.Columns.Add("PatientId", "ID");
            patientsGrid.Columns.Add("FullName", "Nom Complet");
            patientsGrid.Columns.Add("BirthDate", "Date de Naissance");
            patientsGrid.Columns.Add("Age", "Âge");
            patientsGrid.Columns.Add("Gender", "Sexe");
            patientsGrid.Columns.Add("Phone", "Téléphone");
            patientsGrid.Columns.Add("Email", "Email");
            patientsGrid.Columns.Add("RegistrationDate", "Date d'Inscription");

            // Hide ID column
            patientsGrid.Columns["PatientId"].Visible = false;

            // Set column widths
            patientsGrid.Columns["FullName"].FillWeight = 25;
            patientsGrid.Columns["BirthDate"].FillWeight = 15;
            patientsGrid.Columns["Age"].FillWeight = 8;
            patientsGrid.Columns["Gender"].FillWeight = 10;
            patientsGrid.Columns["Phone"].FillWeight = 15;
            patientsGrid.Columns["Email"].FillWeight = 20;
            patientsGrid.Columns["RegistrationDate"].FillWeight = 15;

            // Style the grid
            patientsGrid.DefaultCellStyle.SelectionBackColor = Color.FromArgb(59, 130, 246);
            patientsGrid.DefaultCellStyle.SelectionForeColor = Color.White;
            patientsGrid.AlternatingRowsDefaultCellStyle.BackColor = Color.FromArgb(248, 250, 252);

            this.Controls.Add(patientsGrid);
        }

        private void CreateActionButtons()
        {
            var buttonPanel = new Panel
            {
                Dock = DockStyle.Bottom,
                Height = 70,
                BackColor = Color.White,
                Padding = new Padding(20)
            };

            addButton = CreateActionButton("Ajouter Patient", Color.FromArgb(16, 185, 129), 20);
            editButton = CreateActionButton("Modifier", Color.FromArgb(59, 130, 246), 160);
            deleteButton = CreateActionButton("Supprimer", Color.FromArgb(239, 68, 68), 300);

            addButton.Click += AddButton_Click;
            editButton.Click += EditButton_Click;
            deleteButton.Click += DeleteButton_Click;

            buttonPanel.Controls.AddRange(new Control[] { addButton, editButton, deleteButton });
            this.Controls.Add(buttonPanel);
        }

        private Button CreateActionButton(string text, Color backgroundColor, int x)
        {
            return new Button
            {
                Text = text,
                Font = new Font("Segoe UI", 10, FontStyle.Bold),
                ForeColor = Color.White,
                BackColor = backgroundColor,
                FlatStyle = FlatStyle.Flat,
                Size = new Size(120, 35),
                Location = new Point(x, 15),
                Cursor = Cursors.Hand
            };
        }

        private void LoadPatients()
        {
            try
            {
                var patients = _context.Patients
                    .Where(p => p.IsActive)
                    .OrderBy(p => p.LastName)
                    .ThenBy(p => p.FirstName)
                    .ToList();

                patientsGrid.Rows.Clear();

                foreach (var patient in patients)
                {
                    patientsGrid.Rows.Add(
                        patient.PatientId,
                        patient.FullName,
                        patient.BirthDate.ToString("dd/MM/yyyy"),
                        patient.Age,
                        patient.Gender,
                        patient.Phone,
                        patient.Email,
                        patient.RegistrationDate.ToString("dd/MM/yyyy")
                    );
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Erreur lors du chargement des patients: {ex.Message}", "Erreur",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void SearchTextBox_TextChanged(object sender, EventArgs e)
        {
            string searchTerm = searchTextBox.Text.ToLower();

            foreach (DataGridViewRow row in patientsGrid.Rows)
            {
                if (row.IsNewRow) continue;

                bool visible = string.IsNullOrEmpty(searchTerm) ||
                    row.Cells["FullName"].Value?.ToString().ToLower().Contains(searchTerm) == true ||
                    row.Cells["Phone"].Value?.ToString().Contains(searchTerm) == true ||
                    row.Cells["Email"].Value?.ToString().ToLower().Contains(searchTerm) == true;

                row.Visible = visible;
            }
        }

        private void AddButton_Click(object sender, EventArgs e)
        {
            var addForm = new PatientAddEditForm(_context);
            if (addForm.ShowDialog() == DialogResult.OK)
            {
                LoadPatients();
            }
        }

        private void EditButton_Click(object sender, EventArgs e)
        {
            if (patientsGrid.SelectedRows.Count == 0)
            {
                MessageBox.Show("Veuillez sélectionner un patient à modifier.", "Information",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            int patientId = (int)patientsGrid.SelectedRows[0].Cells["PatientId"].Value;
            var editForm = new PatientAddEditForm(_context, patientId);
            if (editForm.ShowDialog() == DialogResult.OK)
            {
                LoadPatients();
            }
        }

        private void DeleteButton_Click(object sender, EventArgs e)
        {
            if (patientsGrid.SelectedRows.Count == 0)
            {
                MessageBox.Show("Veuillez sélectionner un patient à supprimer.", "Information",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            var result = MessageBox.Show("Êtes-vous sûr de vouloir supprimer ce patient?", "Confirmation",
                MessageBoxButtons.YesNo, MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                try
                {
                    int patientId = (int)patientsGrid.SelectedRows[0].Cells["PatientId"].Value;
                    var patient = _context.Patients.Find(patientId);
                    if (patient != null)
                    {
                        patient.IsActive = false; // Soft delete
                        _context.SaveChanges();
                        LoadPatients();
                        MessageBox.Show("Patient supprimé avec succès.", "Succès",
                            MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"Erreur lors de la suppression: {ex.Message}", "Erreur",
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }
    }
}
