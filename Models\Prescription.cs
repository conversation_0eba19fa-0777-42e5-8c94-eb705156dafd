using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace CleverMC.Models
{
    public class Prescription
    {
        [Key]
        public int PrescriptionId { get; set; }

        [Required]
        public int RecordId { get; set; }

        [Required]
        public int MedicationId { get; set; }

        [StringLength(100)]
        public string? Dosage { get; set; }

        [StringLength(100)]
        public string? Frequency { get; set; }

        public int? DurationDays { get; set; }

        public string? Instructions { get; set; }

        public int? Quantity { get; set; }

        public bool IsDispensed { get; set; } = false;

        public DateTime PrescribedDate { get; set; } = DateTime.Now;

        // Navigation properties
        [ForeignKey("RecordId")]
        public virtual MedicalRecord MedicalRecord { get; set; } = null!;

        [ForeignKey("MedicationId")]
        public virtual Medication Medication { get; set; } = null!;
    }
}
