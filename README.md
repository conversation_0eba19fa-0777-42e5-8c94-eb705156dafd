# CleverMC - Système de Gestion de Clinique Médicale

## Description
CleverMC est un système complet de gestion de clinique médicale développé en C# avec Windows Forms et Entity Framework Core. Le système offre une interface utilisateur moderne en français pour gérer tous les aspects d'une clinique médicale.

## Fonctionnalités

### ✅ Implémentées
- **Tableau de Bord** : Vue d'ensemble avec statistiques et graphiques
- **Gestion des Patients** : Ajouter, modifier, supprimer et rechercher des patients
- Interface utilisateur moderne avec design responsive
- Base de données SQL Server avec Entity Framework Core

### 🚧 En Développement
- Gestion des Médecins
- Gestion des Rendez-vous
- Dossiers Médicaux
- Facturation
- Gestion des Médicaments
- Rapports et Statistiques

## Prérequis

- .NET 8.0 ou supérieur
- SQL Server (LocalDB ou SQL Server Express)
- Visual Studio 2022 ou Visual Studio Code

## Installation

1. **<PERSON><PERSON><PERSON> le projet**
   ```bash
   git clone [URL_DU_REPO]
   cd CleverMC
   ```

2. **Restaurer les packages NuGet**
   ```bash
   dotnet restore
   ```

3. **Configurer la base de données**
   - Assurez-vous que SQL Server est installé et en cours d'exécution
   - Exécutez le script SQL fourni pour créer la base de données `MedicalClinicDB`
   - Modifiez la chaîne de connexion dans `appsettings.json` si nécessaire

4. **Exécuter les migrations (si nécessaire)**
   ```bash
   dotnet ef database update
   ```

5. **Lancer l'application**
   ```bash
   dotnet run
   ```

## Structure du Projet

```
CleverMC/
├── Models/                 # Modèles de données
│   ├── Patient.cs
│   ├── Doctor.cs
│   ├── Appointment.cs
│   └── ...
├── Data/                   # Contexte de base de données
│   └── MedicalClinicDbContext.cs
├── Forms/                  # Formulaires Windows Forms
│   ├── PatientManagementForm.cs
│   ├── PatientAddEditForm.cs
│   └── ...
├── Form1.cs               # Formulaire principal
├── Program.cs             # Point d'entrée
└── README.md
```

## Base de Données

Le système utilise une base de données SQL Server avec les tables suivantes :

- **PATIENTS** : Informations des patients
- **DOCTORS** : Informations des médecins
- **APPOINTMENTS** : Rendez-vous
- **MEDICAL_RECORDS** : Dossiers médicaux
- **MEDICATIONS** : Médicaments
- **PRESCRIPTIONS** : Ordonnances
- **BILLING** : Facturation
- **LAB_TESTS** : Tests de laboratoire
- Et plus...

## Utilisation

### Tableau de Bord
- Vue d'ensemble des statistiques de la clinique
- Graphiques des rendez-vous et revenus
- Cartes d'information rapide

### Gestion des Patients
- **Ajouter** : Cliquez sur "Ajouter Patient" pour créer un nouveau patient
- **Modifier** : Sélectionnez un patient et cliquez sur "Modifier"
- **Supprimer** : Sélectionnez un patient et cliquez sur "Supprimer" (suppression logique)
- **Rechercher** : Utilisez la barre de recherche pour filtrer les patients

## Captures d'Écran

[Les captures d'écran seront ajoutées ici]

## Technologies Utilisées

- **Framework** : .NET 8.0
- **Interface** : Windows Forms
- **Base de données** : SQL Server
- **ORM** : Entity Framework Core
- **Graphiques** : System.Windows.Forms.DataVisualization
- **Langue** : Français

## Contribution

1. Fork le projet
2. Créez une branche pour votre fonctionnalité (`git checkout -b feature/AmazingFeature`)
3. Committez vos changements (`git commit -m 'Add some AmazingFeature'`)
4. Push vers la branche (`git push origin feature/AmazingFeature`)
5. Ouvrez une Pull Request

## Licence

Ce projet est sous licence MIT. Voir le fichier `LICENSE` pour plus de détails.

## Contact

Pour toute question ou suggestion, n'hésitez pas à ouvrir une issue sur GitHub.

## Roadmap

- [ ] Gestion complète des médecins
- [ ] Système de rendez-vous avec calendrier
- [ ] Module de facturation avancé
- [ ] Rapports et statistiques détaillés
- [ ] Système de notifications
- [ ] Sauvegarde automatique
- [ ] Interface web (future version)
